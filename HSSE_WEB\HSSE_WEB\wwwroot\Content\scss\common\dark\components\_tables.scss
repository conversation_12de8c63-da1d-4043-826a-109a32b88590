/* Tables */

.table {
  margin-bottom: 0;
  thead {
    th {
      border-top: 0;
      border-bottom-width: 1px;
      font-weight: 700;
      font-size: 0.75rem;
      color: $body-color;
      letter-spacing: 0.031rem;
      padding: 0.312rem 0.937rem;
      i {
        margin-left: 0.325rem;
      }
    }
  }
  th,
  td {
    vertical-align: middle;
    line-height: 1.462;
    white-space: nowrap;
  }
  td {
    font-size: $default-font-size;
    img {
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
    .badge {
      margin-bottom: 0;
    }
  }
  &.table-borderless {
    border: none;
    tr,
    td,
    th {
      border: none;
    }
  }
  &.table-bordered {
    border-top: 1px solid $border-color;
    thead {
      tr {
        th {
          padding-top: 0.312rem;
        }
      }
    }
  }
}

/* Standard Page Container Styles - Dark Theme */
.page-container {
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

.view-document-section {
  margin-bottom: 1.5rem;

  .document-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem 0.375rem 0 0;

    .document-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: $body-color;
      text-transform: capitalize;
    }

    .document-subtitle {
      margin: 0.25rem 0 0 0;
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .document-content {
    background: $card-bg;
    border: 1px solid $border-color;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;

    .table-container {
      padding: 0;

      .table-responsive {
        margin: 0;
        border-radius: 0;

        .table {
          margin-bottom: 0;
          border-radius: 0;

          thead th {
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 2px solid $border-color;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            color: $body-color;
            padding: 0.75rem;
          }

          tbody td {
            padding: 0.75rem;
            border-bottom: 1px solid $border-color;
            font-size: 0.875rem;
          }

          tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
          }

          tbody tr:last-child td {
            border-bottom: none;
          }
        }
      }
    }
  }
}

/* Standard Card Container - Dark Theme */
.standard-card-container {
  margin-bottom: 1.5rem;

  .card {
    border: 1px solid $border-color;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);

    .card-header {
      background-color: rgba(255, 255, 255, 0.05);
      border-bottom: 1px solid $border-color;
      padding: 1rem 1.25rem;

      .card-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: $body-color;
        text-transform: capitalize;
      }
    }

    .card-body {
      padding: 1.25rem;

      .table-responsive {
        margin: -0.25rem 0 0 0;

        .table {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* Grid Layout for Consistent Spacing - Dark Theme */
.content-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;

  &.two-column {
    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }

  &.three-column {
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (min-width: 992px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
