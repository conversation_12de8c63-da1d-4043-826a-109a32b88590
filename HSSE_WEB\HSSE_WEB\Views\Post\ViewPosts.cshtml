@model dynamic
@{
    ViewData["Title"] = "View Posts";
}
<style>
    .select2.select2-container.select2-container--default {
        width: 100% !important;
    }
</style>

<!-- Standard View Document Section for Posts -->
<div class="view-document-section">
    <div class="document-header d-flex">
        <div class="document-info">
            <h4 class="document-title">@ViewData["Title"]</h4>
            <p class="document-subtitle">Browse and manage posts in the system</p>
        </div>
        <div class="document-actions">
            <select id="postFilter" onchange="handlePostFilterChange()" class="form-control mr-2" style="width: auto;">
                <option value="false">All Posts</option>
                <option value="true">My Posts</option>
            </select>
            <button id="btn-toggle-view" class="btn btn-primary btn-sm">
                <i class="fa fa-table"></i> Table View
            </button>
        </div>
    </div>
    <div class="document-content">
        <!-- Card View Container -->
        <div id="posts-container" class="row py-4" style="overflow-y: auto; min-height: 400px;"></div>

        <!-- Table View Container (initially hidden) -->
        <div id="table-container" class="table-container" style="display: none;">
            <div class="table-responsive">
                <table id="posts-table" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th>Created Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Table data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Comment Modal -->
<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-md">
        <div class="modal-content">

            <!-- Modal Header (Fixed) -->
            <div class="modal-header">
                <h5 class="modal-title">Comments</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <!-- Modal Body (Flexible Layout) -->
            <div class="modal-body d-flex flex-column" style="height: 500px;">

                <!-- Scrollable Comment List -->
                <div id="comment-list" class="mb-3 overflow-auto" style="flex: 1 1 auto;"></div>

                <!-- Input Area (Fixed at bottom) -->
                <div class="input-group">
                    <textarea id="new-comment-text" class="form-control" placeholder="Write a comment..." rows="2" style="resize: none;"></textarea>
                    <button class="btn btn-primary" id="send-comment-btn">Send</button>
                </div>

            </div>

        </div>
    </div>
</div>
<!-- Edit Modal -->
<div class="modal fade" id="editPostModal" tabindex="-1" aria-labelledby="editPostModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPostModalLabel">Edit Post</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editPostForm" class="form-sample">
                    <input type="hidden" id="EditPostId" />
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="EditTitle" class="col-sm-4 col-form-label">Title <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="EditTitle" required />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="EditPostType" class="col-sm-4 col-form-label">Post Type <span class="text-danger">*</span></label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="EditPostType" required />
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="EditLocation" class="col-sm-4 col-form-label">Location</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="EditLocation" />
                                </div>
                            </div>
                            @*     <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Status</label>
                                <div class="form-check">
                                    <label class="form-check-label">
                                        <input type="checkbox" class="form-check-input" id="EditStatus" />
                                    </label>
                                </div>
                            </div> *@
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label for="EditTaggedCategoryId" class="col-sm-4 col-form-label">Category</label>
                                <div class="col-sm-8">
                                    <select name="EditTaggedCategoryId" id="EditTaggedCategoryId" class="form-control">
                                        <option value="">-- Select Category --</option>
                                        @foreach (var cat in Model.MstPostCategory)
                                        {
                                            <option value="@cat.CatId">@cat.CategoryName</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label for="EditFacilityId" class="col-sm-4 col-form-label">Facility</label>
                                <div class="col-sm-8">
                                    <select name="EditFacilityId" id="EditFacilityId" class="form-control">
                                        <option value="">-- Select Facility --</option>
                                        @foreach (var fac in Model.Facilities)
                                        {
                                            <option value="@fac.FacilityId">@fac.FacilityName</option>
                                        }
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Upload Image</label>
                              
                                        <div class="col-sm-8">
                                            <div class="custom-file-upload w-100">
                                                <input type="hidden" id="ExistingThumbnailPath" />

                                                <input type="file" class="d-none" id="AnnouncementFile" name="ThumbnailImage" accept="image/*" />
                                                <div class="input-group">
                                                    <input type="text" class="form-control file-upload-info" id="announcementThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                                    <div class="input-group-append">
                                                        <button class="btn btn-primary btn-sm" type="button" id="announcementThumbnailBtn">Upload</button>
                                                        <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAnnouncementThumbnailBtn">Remove</button>
                                                    </div>
                                                </div>
                                                <div class="mt-2 d-none" id="announcementThumbnailPreviewContainer">
                                                    <img id="announcementThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                                </div>
                                            </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description</label>
                                <div class="col-sm-12">
                                    <textarea id="editTinyMceExample" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" form="editPostForm" class="btn btn-primary">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="assignModal" tabindex="-1" role="dialog" aria-labelledby="assignModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignModalLabel">Assign Post</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="assignForm">
                    <input type="hidden" id="assignPostId" />
                 
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Select Users</label>
                                <div class="col-sm-9">
                                    <select id="assignedTo" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                        <option value="">Loading...</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary" onclick="submitAssignment()">Assign</button>
                </form>
            </div>
        </div>
    </div>
</div>
@await Html.PartialAsync("~/Views/Shared/_CompletePostModal.cshtml") 
<script>
                        var getAllGetPostComments = '@Url.Action("GetAllGetPostComments", "Post")?postId=';

    var getPostsByUserId = '@Url.Action("GetPostsByUserId", "Post")';
    var toggleLikeUrl = '@Url.Action("ToggleLike", "Post")?postId=';
    var userId = '@ViewBag.UserId'; // Set this in your controller or layout
        const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production
    var createComment =  '@Url.Action("CreateComment", "Post")';
    var getUserByFacilityId = '@Url.Action("GetUserByFacilityId", "Post")?facilityId=';
    var assignPost = '@Url.Action("AssignPost", "Post")';
 var GetById =  '@Url.Action("GetById", "Post")';
       var deletePostUrl = '@Url.Action("DeletePost", "Post")';
             var updateFollowUpStatus =  '@Url.Action("UpdateFollowUpStatus", "Post")';
                    var update = '@Url.Action("Update", "Post")'; 
</script>

@section Scripts {
    <script src="~/js/Post/viewPosts.js"></script>

    <script src="~/js/Post/listPosts.js"></script>
} 