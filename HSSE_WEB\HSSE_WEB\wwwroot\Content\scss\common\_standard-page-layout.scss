/* 
 * Standard Page Layout Styles for HSSE_WEB Application
 * This file contains standardized styles for consistent page layouts and table formatting
 */

/* Page Container Base Styles */
.page-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  
  /* Ensure proper spacing within content wrapper */
  .content-wrapper & {
    padding: 0;
  }
}

/* View Document Section Styles */
.view-document-section {
  margin-bottom: 1.5rem;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .document-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color, #dee2e6);
    
    .document-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      text-transform: capitalize;
      line-height: 1.4;
    }
    
    .document-subtitle {
      margin: 0.25rem 0 0 0;
      font-size: 0.875rem;
      opacity: 0.8;
      line-height: 1.4;
    }
    
    .document-actions {
      margin-left: auto;
      display: flex;
      gap: 0.5rem;
      align-items: center;
    }
    
    /* Header with actions layout */
    &.d-flex {
      display: flex !important;
      align-items: center;
      justify-content: space-between;
      
      .document-info {
        flex: 1;
      }
    }
  }
  
  .document-content {
    border-top: none;
    
    .table-container {
      padding: 0;
      
      .table-responsive {
        margin: 0;
        border-radius: 0;
        border: none;
        
        .table {
          margin-bottom: 0;
          border-radius: 0;
          
          /* Enhanced table header styling */
          thead th {
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            padding: 0.75rem;
            border-bottom: 2px solid var(--border-color, #dee2e6);
            position: sticky;
            top: 0;
            z-index: 10;
          }
          
          /* Enhanced table body styling */
          tbody {
            td {
              padding: 0.75rem;
              border-bottom: 1px solid var(--border-color, #dee2e6);
              font-size: 0.875rem;
              vertical-align: middle;
            }
            
            tr {
              transition: background-color 0.15s ease-in-out;
              
              &:hover {
                background-color: var(--hover-bg, rgba(0, 0, 0, 0.05));
              }
              
              &:last-child td {
                border-bottom: none;
              }
            }
          }
          
          /* Action buttons in tables */
          .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            
            i {
              font-size: 0.875rem;
            }
          }
          
          /* Status badges */
          .badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
          }
        }
      }
    }
    
    /* Empty state styling */
    .empty-state {
      text-align: center;
      padding: 3rem 1.5rem;
      color: var(--text-muted, #6c757d);
      
      .empty-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
      }
      
      .empty-message {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
      }
      
      .empty-description {
        font-size: 0.875rem;
        opacity: 0.8;
      }
    }
  }
}

/* Standard Card Container Styles */
.standard-card-container {
  margin-bottom: 1.5rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .card {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid var(--border-color, #dee2e6);
    
    .card-header {
      border-bottom: 1px solid var(--border-color, #dee2e6);
      padding: 1rem 1.25rem;
      
      .card-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        text-transform: capitalize;
        line-height: 1.4;
      }
      
      .card-actions {
        margin-left: auto;
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
      
      /* Header with actions layout */
      &.d-flex {
        display: flex !important;
        align-items: center;
        justify-content: space-between;
      }
    }
    
    .card-body {
      padding: 1.25rem;
      
      .table-responsive {
        margin: -0.25rem 0 0 0;
        
        .table {
          margin-bottom: 0;
        }
      }
      
      /* Form styling within cards */
      .form-group {
        margin-bottom: 1rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
      
      .form-actions {
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid var(--border-color, #dee2e6);
        text-align: right;
        
        .btn + .btn {
          margin-left: 0.5rem;
        }
      }
    }
  }
}

/* Content Grid Layout */
.content-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
  
  &.two-column {
    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }
  
  &.three-column {
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (min-width: 992px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  
  &.four-column {
    @media (min-width: 576px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
    @media (min-width: 992px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

/* Responsive Table Enhancements */
@media (max-width: 767.98px) {
  .table-responsive {
    .table {
      font-size: 0.8rem;
      
      th, td {
        padding: 0.5rem 0.25rem;
        white-space: normal;
      }
      
      .btn-sm {
        padding: 0.125rem 0.25rem;
        font-size: 0.7rem;
      }
    }
  }
  
  .view-document-section {
    .document-header {
      padding: 0.75rem 1rem;
      
      .document-title {
        font-size: 1.125rem;
      }
      
      .document-subtitle {
        font-size: 0.8rem;
      }
    }
  }
  
  .standard-card-container {
    .card {
      .card-header {
        padding: 0.75rem 1rem;
        
        .card-title {
          font-size: 1.125rem;
        }
      }
      
      .card-body {
        padding: 1rem;
      }
    }
  }
}

/* Print Styles */
@media print {
  .page-container {
    box-shadow: none;
  }
  
  .view-document-section,
  .standard-card-container .card {
    border: 1px solid #000 !important;
    box-shadow: none !important;
    break-inside: avoid;
  }
  
  .document-header,
  .card-header {
    background: #f8f9fa !important;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
  
  .table {
    thead th {
      background: #f8f9fa !important;
      -webkit-print-color-adjust: exact;
      color-adjust: exact;
    }
  }
  
  .btn {
    display: none !important;
  }
}
