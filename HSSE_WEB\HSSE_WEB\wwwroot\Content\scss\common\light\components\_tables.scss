/* Tables */

.table {
  margin-bottom: 0;
  thead {
    th {
      border-top: 0;
      border-bottom-width: 1px;
      font-weight: 700;
      font-size: 0.75rem;
      color: #7987a1;
      letter-spacing: 0.031rem;
      padding: 0.312rem 0.937rem;
      i {
        margin-left: 0.325rem;
      }
    }
  }
  th,
  td {
    vertical-align: middle;
    line-height: 1.462;
    white-space: nowrap;
  }
  td {
    font-size: $default-font-size;
    img {
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
    .badge {
      margin-bottom: 0;
    }
  }
  &.table-borderless {
    border: none;
    tr,
    td,
    th {
      border: none;
    }
  }
  &.table-bordered {
    border-top: 1px solid $border-color;
    thead {
      tr {
        th {
          padding-top: 0.312rem;
        }
      }
    }
  }
}

/* Standard Page Container Styles */
.page-container {
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

.view-document-section {
  margin-bottom: 1.5rem;

  .document-header {
    background: #f8f9fa;
    padding: 1rem 1.25rem;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.375rem 0.375rem 0 0;

    .document-title {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #495057;
      text-transform: capitalize;
    }

    .document-subtitle {
      margin: 0.25rem 0 0 0;
      font-size: 0.875rem;
      color: #6c757d;
    }
  }

  .document-content {
    background: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;

    .table-container {
      padding: 0;

      .table-responsive {
        margin: 0;
        border-radius: 0;

        .table {
          margin-bottom: 0;
          border-radius: 0;

          thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.5px;
            color: #495057;
            padding: 0.75rem;
          }

          tbody td {
            padding: 0.75rem;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.875rem;
          }

          tbody tr:hover {
            background-color: #f8f9fa;
          }

          tbody tr:last-child td {
            border-bottom: none;
          }
        }
      }
    }
  }
}

/* Standard Card Container */
.standard-card-container {
  margin-bottom: 1.5rem;

  .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);

    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #dee2e6;
      padding: 1rem 1.25rem;

      .card-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        text-transform: capitalize;
      }
    }

    .card-body {
      padding: 1.25rem;

      .table-responsive {
        margin: -0.25rem 0 0 0;

        .table {
          margin-bottom: 0;
        }
      }
    }
  }
}

/* Grid Layout for Consistent Spacing */
.content-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;

  &.two-column {
    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }

  &.three-column {
    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (min-width: 992px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
