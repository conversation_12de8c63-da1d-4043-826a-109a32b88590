@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "My Inspections";
    var isAppAdmin = Convert.ToBoolean(Context.Session.GetString("IsAppAdmin"));
    var isDeptAdmin = Convert.ToBoolean(Context.Session.GetString("IsDepartmentAdmin"));
}

<!-- Standard View Document Section for Inspections Table -->
<div class="view-document-section">
    <div class="document-header d-flex">
        <div class="document-info">
            <h4 class="document-title">@ViewData["Title"]</h4>
            <p class="document-subtitle">View and manage your assigned inspections</p>
        </div>
        <div class="document-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                <i class="mdi mdi-refresh"></i> Refresh
            </button>
        </div>
    </div>
    <div class="document-content">
        <div class="table-container">
            <div class="table-responsive">
                <table id="userInspectionsTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Reference No</th>
                            <th>Facility</th>
                            <th>Date</th>
                            <th>Inspector Name</th>
                            <th>Contact Person Name</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded by DataTable -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Complete Inspection Modal -->
<div class="modal fade" id="completeInspectionModal" tabindex="-1" role="dialog" aria-labelledby="completeInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="completeInspectionForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeInspectionModalLabel">Complete Inspection</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="completeInspectionId" name="inspectionId" />
                    <div class="form-group">
                        <label>After Image</label>
                        <div class="input-group  mt-3">
                            <input type="file" class="file-upload-default d-none" id="afterImageInput">
                            <input type="text" class="form-control file-upload-info" disabled placeholder="Upload image">
                            <div class="input-group-append">
                                <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                            </div>

                        </div>
                    </div>
                    <div class="form-group">
                        <label for="completionDateInput">Completion Date</label>
                        <input type="date" class="form-control" id="completionDateInput" name="completionDate" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Complete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rectification Remarks Modal -->
<div class="modal fade" id="rectificationModal" tabindex="-1" role="dialog" aria-labelledby="rectificationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="rectificationForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="rectificationModalLabel">Cannot Rectify - Remarks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="rectificationInspectionId" name="inspectionId" />
                    <div class="form-group">
                        <label for="rectificationRemarks">Remarks</label>
                        <textarea class="form-control" id="rectificationRemarks" name="remarks" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Inspection Modal -->
<div class="modal fade" id="editInspectionModal" tabindex="-1" role="dialog" aria-labelledby="editInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form id="editInspectionForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="editInspectionModalLabel">Edit Inspection</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                   @*  <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Reference No</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="editReferenceNo" name="ReferenceNo" readonly />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-4 col-form-label">Date and Time</label>
                                <div class="col-sm-8">
                                    <input type="text" class="form-control" id="editDateTime" name="DateTime" readonly />
                                </div>
                            </div>
                        </div>
                    </div> *@
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Facility<span class="text-danger">*</span></label>
                                <select class="form-control" id="editFacilityId" name="FacilityId" required></select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Location<span class="text-danger">*</span></label>
                                <input class="form-control" id="editLocationId" name="LocationId" placeholder="Location"/>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Type of Inspection<span class="text-danger">*</span></label>
                                <div id="user-typeahead">
                                    <input type="text" class="typeahead form-control" id="edit-user-search" placeholder="Search Category">
                                    <input type="hidden" id="editSelectedCategoryId" name="selectedCategoryId" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Action Party<span class="text-danger">*</span></label>
                                <select class="form-control" id="editActionPartyId" name="ActionPartyId">
                                    <option value="">Select Action Party</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Contact Person<span class="text-danger">*</span></label>
                                <select class="form-control" id="editContactPersonId" name="ContactPersonId">
                                    <option value="">Select Contact Person</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Observation</label>
                                <textarea class="form-control" id="tinyMceExample" name="Observation"></textarea>
                                <div class="input-group  mt-3">
                                    <input type="file"  class="file-upload-default d-none" id="editObservationAttachment">
                                    <input type="text" class="form-control file-upload-info" disabled placeholder="Upload Doc">
                                    <div class="input-group-append">
                                        <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    
                    </div>
                    <div class="row">
                            <div class="col-md-12">
                            <div class="form-group">
                                <label>Recommendation</label>
                                <textarea class="form-control" id="editTinyMceExample" name="Recommendation"></textarea>
                                <div class="input-group  mt-3">
                                    <input type="file"  class="file-upload-default d-none" id="editRecommendationAttachment">
                                    <input type="text" class="form-control file-upload-info" disabled placeholder="Upload Doc">
                                    <div class="input-group-append">
                                        <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Person Conducting Inspection</label>
                                <input type="text" class="form-control" id="editInspectorName" name="InspectorName" readonly />
                            </div>
                        </div>
                     @*    <div class="col-md-4">
                            <div class="form-group">
                                <label>Status<span class="text-danger">*</span></label>
                                <select class="form-control" id="editStatus" name="Status"></select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Verification<span class="text-danger">*</span></label>
                                <select class="form-control" id="editVerification" name="Verification"></select>
                            </div>
                        </div> *@
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    var isAppAdmin = @isAppAdmin.ToString().ToLower(); // true or false in JS
    var isDeptAdmin =  @isDeptAdmin.ToString().ToLower();
            var insertOrUpdateInspection = '@Url.Action("InsertOrUpdateInspection", "Inspection")';
                       const loggedInUserName = '@(Model.NewUser?.Username ?? "Unknown User")';
               var getUsersByFacilityId = '@Url.Action("GetUsersByFacilityId", "User")?facilityId=';
                var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';

            var completeInspection = '@Url.Action("CompleteInspection", "Inspection")';
            var getUserInspections = '@Url.Action("GetUserInspections", "Inspection")';
            var updateVerificationStatus = '@Url.Action("UpdateVerificationStatus", "Inspection")';
            const basePath = '@Url.Content("~/")';
    var existingUsers = [
    @foreach (var user in Model.InspectionCategory)
    {
        @: { id: "@user.InspectionCategoryId", name: "@user.CategoryName" },
    }
            ];

    // Refresh table function
    function refreshTable() {
        if ($.fn.DataTable.isDataTable('#userInspectionsTable')) {
            $('#userInspectionsTable').DataTable().ajax.reload();
        }
    }
</script>
@section Scripts {

    <script src="~/js/Inspection/userInspections.js"></script>
} 