@*
    Standard Page Template for HSSE_WEB Application
    
    This template provides a consistent structure for all pages in the application.
    It ensures proper container usage and standardized table formatting.
    
    Usage:
    1. Copy this structure to your page
    2. Replace the content sections with your specific content
    3. Ensure all tables are wrapped in the view-document-section structure
*@

@{
    ViewData["Title"] = "Page Title"; // Set your page title here
}

<!-- Standard Page Container -->
<div class="page-container">
    
    <!-- View Document Section for Main Content -->
    <div class="view-document-section">
        <div class="document-header">
            <h4 class="document-title">@ViewData["Title"]</h4>
            <p class="document-subtitle">Brief description of the page content</p>
        </div>
        <div class="document-content">
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Column 1</th>
                                <th>Column 2</th>
                                <th>Column 3</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Table content goes here -->
                            <tr>
                                <td>Sample Data 1</td>
                                <td>Sample Data 2</td>
                                <td>Sample Data 3</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="mdi mdi-pencil"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Alternative: Standard Card Container (for forms or other content) -->
    <div class="standard-card-container">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">Form Section Title</h4>
            </div>
            <div class="card-body">
                <!-- Form or other content goes here -->
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Field Label</label>
                                <input type="text" class="form-control" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Another Field</label>
                                <select class="form-control">
                                    <option>Option 1</option>
                                    <option>Option 2</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 text-end">
                            <button type="submit" class="btn btn-primary">Submit</button>
                            <button type="reset" class="btn btn-light">Cancel</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Multiple Tables in Grid Layout -->
    <div class="content-grid two-column">
        <div class="view-document-section">
            <div class="document-header">
                <h5 class="document-title">Related Data Table 1</h5>
            </div>
            <div class="document-content">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Item 1</td>
                                    <td><span class="badge badge-success">Active</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="view-document-section">
            <div class="document-header">
                <h5 class="document-title">Related Data Table 2</h5>
            </div>
            <div class="document-content">
                <div class="table-container">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Event</th>
                                    <th>User</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-01</td>
                                    <td>Login</td>
                                    <td>John Doe</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

@section Scripts {
    <script>
        // Page-specific JavaScript goes here
        $(document).ready(function() {
            // Initialize DataTables or other plugins
            $('.table').DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'desc']]
            });
        });
    </script>
}
