# Standard Page Layout Guide for HSSE_WEB Application

## Overview

This guide provides standardized page layout structures to ensure consistency across all pages in the HSSE_WEB application. All page content should be properly contained within containers and view document sections with consistent table formatting.

## Key Principles

1. **Consistent Container Usage**: All pages should use the standardized container structure
2. **Uniform Table Formatting**: All tables should follow the same styling and wrapper structure
3. **Responsive Design**: All layouts should work across different screen sizes
4. **Accessibility**: Proper semantic structure and ARIA labels
5. **Maintainability**: Clear, consistent code structure

## Standard Page Structure

### Basic Page Template

```html
<!-- Standard Page Container (automatically added by _Layout.cshtml) -->
<div class="page-container">
    
    <!-- View Document Section for Main Content -->
    <div class="view-document-section">
        <div class="document-header">
            <h4 class="document-title">Page Title</h4>
            <p class="document-subtitle">Brief description of the page content</p>
        </div>
        <div class="document-content">
            <div class="table-container">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <!-- Table content -->
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
```

### Header with Actions

For pages that need action buttons or filters in the header:

```html
<div class="view-document-section">
    <div class="document-header d-flex">
        <div class="document-info">
            <h4 class="document-title">Page Title</h4>
            <p class="document-subtitle">Description</p>
        </div>
        <div class="document-actions">
            <button class="btn btn-sm btn-outline-primary">
                <i class="mdi mdi-refresh"></i> Refresh
            </button>
            <button class="btn btn-sm btn-primary">
                <i class="mdi mdi-plus"></i> Add New
            </button>
        </div>
    </div>
    <!-- ... rest of content -->
</div>
```

## CSS Classes Reference

### Container Classes

- `.page-container`: Main page wrapper (automatically applied)
- `.view-document-section`: Standard section for tables and content
- `.standard-card-container`: Alternative container for forms
- `.content-grid`: Grid layout for multiple sections

### Document Section Classes

- `.document-header`: Header area with title and actions
- `.document-title`: Main title styling
- `.document-subtitle`: Subtitle/description styling
- `.document-actions`: Container for action buttons
- `.document-content`: Main content area

### Table Classes

- `.table-container`: Wrapper for table content
- `.table-responsive`: Bootstrap responsive table wrapper
- `.table`: Base table class
- `.table-hover`: Adds hover effects
- `.table-bordered`: Adds borders
- `.table-striped`: Alternating row colors

## Layout Patterns

### 1. Single Table Page

Use for pages with one main data table:

```html
<div class="view-document-section">
    <div class="document-header">
        <h4 class="document-title">Users List</h4>
        <p class="document-subtitle">Manage system users</p>
    </div>
    <div class="document-content">
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover">
                    <!-- Table content -->
                </table>
            </div>
        </div>
    </div>
</div>
```

### 2. Form with Table Page

Use for pages with both forms and tables:

```html
<!-- Form Section -->
<div class="standard-card-container">
    <div class="card">
        <div class="card-header">
            <h4 class="card-title">Add New Item</h4>
        </div>
        <div class="card-body">
            <!-- Form content -->
        </div>
    </div>
</div>

<!-- Table Section -->
<div class="view-document-section">
    <!-- Table content -->
</div>
```

### 3. Multiple Tables Page

Use for pages with multiple related tables:

```html
<div class="content-grid two-column">
    <div class="view-document-section">
        <div class="document-header">
            <h5 class="document-title">Table 1</h5>
        </div>
        <div class="document-content">
            <!-- Table 1 content -->
        </div>
    </div>
    
    <div class="view-document-section">
        <div class="document-header">
            <h5 class="document-title">Table 2</h5>
        </div>
        <div class="document-content">
            <!-- Table 2 content -->
        </div>
    </div>
</div>
```

## Table Formatting Standards

### Basic Table Structure

```html
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Column 1</th>
                <th>Column 2</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Data 1</td>
                <td>Data 2</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="mdi mdi-pencil"></i>
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
</div>
```

### Table Variations

- **Bordered Tables**: Add `table-bordered` class
- **Striped Tables**: Add `table-striped` class
- **Small Tables**: Add `table-sm` class
- **Dark Tables**: Add `table-dark` class

## Migration Guide

### Converting Existing Pages

1. **Remove old container structure**:
   ```html
   <!-- OLD -->
   <div class="col-12 grid-margin">
       <div class="card">
           <div class="card-body">
   ```

2. **Replace with standard structure**:
   ```html
   <!-- NEW -->
   <div class="view-document-section">
       <div class="document-header">
           <h4 class="document-title">Title</h4>
       </div>
       <div class="document-content">
   ```

3. **Update table wrappers**:
   ```html
   <!-- OLD -->
   <div class="table-responsive">
       <table class="table">
   
   <!-- NEW -->
   <div class="table-container">
       <div class="table-responsive">
           <table class="table table-hover">
   ```

## Best Practices

1. **Always use semantic HTML**: Proper heading hierarchy, table structure
2. **Include descriptive subtitles**: Help users understand page purpose
3. **Group related actions**: Use document-actions for buttons and filters
4. **Maintain consistent spacing**: Use standard margin/padding classes
5. **Test responsiveness**: Ensure layouts work on mobile devices
6. **Use appropriate table classes**: Choose the right combination for your needs

## Examples

See the following files for implementation examples:
- `Views/Shared/_StandardPageTemplate.cshtml` - Complete template
- `Views/Inspection/UserInspections.cshtml` - Updated inspection page
- `Views/Post/ViewPosts.cshtml` - Updated posts page

## Quick Reference

### Essential Classes Checklist

✅ **Page Structure**
- `view-document-section` - Main content wrapper
- `document-header` - Header with title
- `document-content` - Content area

✅ **Table Structure**
- `table-container` - Table wrapper
- `table-responsive` - Responsive wrapper
- `table table-hover` - Table with hover effects

✅ **Header Patterns**
- `document-title` - Main title
- `document-subtitle` - Description
- `document-actions` - Action buttons

### Common Mistakes to Avoid

❌ **Don't use**: `col-12 grid-margin` (old pattern)
✅ **Use instead**: `view-document-section`

❌ **Don't use**: Direct `card` without wrapper
✅ **Use instead**: `standard-card-container`

❌ **Don't use**: `table` without proper wrappers
✅ **Use instead**: `table-container > table-responsive > table`

## CSS Files

The standard layout styles are defined in:
- `wwwroot/Content/scss/common/_standard-page-layout.scss`
- `wwwroot/Content/scss/common/light/components/_tables.scss`
- `wwwroot/Content/scss/common/dark/components/_tables.scss`
